"""Pandoc conversion schemas"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field


class PandocConversionRequest(BaseModel):
    """
    Pandoc转换请求架构

    Schema for Pandoc conversion request
    """

    # Input format (auto-detect if not specified)
    from_format: Optional[str] = Field(
        None,
        description="输入格式 (如 'markdown', 'docx') / Input format (e.g., 'markdown', 'docx')",
    )

    # Output format (required)
    to_format: str = Field(
        ...,
        description="输出格式 (如 'html', 'pdf', 'docx') / Output format (e.g., 'html', 'pdf', 'docx')",
    )

    # Template references (by name from database)
    docx_template: Optional[str] = Field(
        None, description="数据库中的DOCX模板名称 / DOCX template name from database"
    )
    csl_style: Optional[str] = Field(
        None, description="数据库中的CSL样式名称 / CSL style name from database"
    )
    bibliography: Optional[str] = Field(
        None,
        description="数据库中的参考文献文件名 / Bibliography file name from database",
    )

    # Additional Pandoc options
    options: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="额外的Pandoc选项 / Additional Pandoc options"
    )

    # Metadata
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="文档元数据 / Document metadata"
    )

    # Output filename (optional, will be auto-generated if not provided)
    output_filename: Optional[str] = Field(
        None, description="自定义输出文件名 / Custom output filename"
    )


class PandocConversionResponse(BaseModel):
    """
    Pandoc转换响应架构

    Schema for Pandoc conversion response
    """

    success: bool = Field(
        ..., description="转换是否成功 / Whether conversion was successful"
    )
    message: str = Field(..., description="响应消息 / Response message")
    output_files: List[str] = Field(
        default_factory=list, description="生成的文件列表 / List of generated files"
    )
    download_url: Optional[str] = Field(
        None, description="单个文件或ZIP的下载URL / Download URL for single file or zip"
    )
    conversion_time: float = Field(
        ..., description="转换耗时(秒) / Conversion time in seconds"
    )
    pandoc_command: Optional[str] = Field(
        None,
        description="执行的Pandoc命令(用于调试) / Executed Pandoc command (for debugging)",
    )


class PandocError(BaseModel):
    """
    Pandoc错误响应架构

    Schema for Pandoc error response
    """

    success: bool = Field(
        False, description="转换是否成功 / Whether conversion was successful"
    )
    message: str = Field(..., description="错误消息 / Error message")
    error_type: str = Field(..., description="错误类型 / Error type")
    pandoc_output: Optional[str] = Field(
        None, description="Pandoc输出信息 / Pandoc output"
    )
    conversion_time: Optional[float] = Field(
        None, description="转换耗时(秒) / Conversion time in seconds"
    )


class ConversionStatus(BaseModel):
    """
    转换状态架构

    Schema for conversion status
    """

    status: str = Field(
        ...,
        description="状态: 'processing', 'completed', 'failed' / Status: 'processing', 'completed', 'failed'",
    )
    progress: Optional[float] = Field(
        None, description="进度百分比 (0-100) / Progress percentage (0-100)"
    )
    message: Optional[str] = Field(None, description="状态消息 / Status message")
    started_at: Optional[str] = Field(None, description="开始时间 / Start time")
    completed_at: Optional[str] = Field(None, description="完成时间 / Completion time")
