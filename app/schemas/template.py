"""Template schemas for API requests and responses"""

from datetime import datetime
from typing import Optional, Dict, Any, List

from pydantic import BaseModel, Field

from app.models.template import ResourceType


class TemplateMetadataSchema(BaseModel):
    """
    模板元数据架构

    Template metadata schema
    """

    name: str = Field(default="", description="模板名称 / Template name")
    display_name: str = Field(default="", description="显示名称 / Display name")
    description: str = Field(default="", description="模板描述 / Template description")
    version: str = Field(default="1.0.0", description="版本号 / Version number")
    author: str = Field(default="", description="作者 / Author")
    tags: List[str] = Field(default=[], description="标签列表 / Tag list")
    supported_formats: List[str] = Field(
        default=[], description="支持的格式 / Supported formats"
    )
    custom_fields: Dict[str, Any] = Field(
        default={}, description="自定义字段 / Custom fields"
    )


class TemplateResourceBase(BaseModel):
    """
    模板资源基础架构

    Base template resource schema
    """

    name: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="唯一的模板名称 / Unique template name",
    )
    display_name: str = Field(
        ..., min_length=1, max_length=255, description="显示名称 / Display name"
    )
    description: Optional[str] = Field(
        None, description="模板描述 / Template description"
    )
    resource_type: ResourceType = Field(..., description="资源类型 / Resource type")
    is_active: bool = Field(True, description="是否活跃 / Is active")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据 / Metadata")


class TemplateResourceCreate(TemplateResourceBase):
    """
    创建模板资源的架构

    Schema for creating template resource
    """

    pass


class TemplateResourceUpdate(BaseModel):
    """
    更新模板资源的架构

    Schema for updating template resource
    """

    display_name: Optional[str] = Field(
        None, min_length=1, max_length=255, description="显示名称 / Display name"
    )
    description: Optional[str] = Field(
        None, description="模板描述 / Template description"
    )
    is_active: Optional[bool] = Field(None, description="是否活跃 / Is active")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据 / Metadata")


class TemplateResourceResponse(TemplateResourceBase):
    """
    模板资源响应架构

    Schema for template resource response
    """

    id: int = Field(..., description="模板ID / Template ID")
    file_path: str = Field(..., description="文件路径 / File path")
    file_size: int = Field(..., description="文件大小(字节) / File size in bytes")
    mime_type: Optional[str] = Field(None, description="MIME类型 / MIME type")
    is_builtin: bool = Field(False, description="是否为内置模板 / Is built-in template")
    created_at: datetime = Field(..., description="创建时间 / Created at")
    updated_at: Optional[datetime] = Field(None, description="更新时间 / Updated at")
    file_exists: bool = Field(..., description="文件是否存在 / File exists")

    model_config = {"from_attributes": True}


class TemplateResourceList(BaseModel):
    """
    模板资源列表响应架构

    Schema for template resource list response
    """

    resources: list[TemplateResourceResponse] = Field(
        ..., description="模板资源列表 / Template resource list"
    )
    total: int = Field(..., description="总数量 / Total count")
    page: int = Field(..., description="当前页码 / Current page")
    page_size: int = Field(..., description="每页大小 / Page size")


class TemplateReloadResponse(BaseModel):
    """
    模板重新加载响应架构

    Schema for template reload response
    """

    success: bool = Field(..., description="是否成功 / Success status")
    message: str = Field(..., description="响应消息 / Response message")
    builtin_loaded: int = Field(
        ..., description="加载的内置模板数量 / Number of built-in templates loaded"
    )
    user_loaded: int = Field(
        ..., description="加载的用户模板数量 / Number of user templates loaded"
    )
    additional_loaded: int = Field(
        ..., description="加载的额外模板数量 / Number of additional templates loaded"
    )
    errors: int = Field(..., description="错误数量 / Number of errors")


class TemplateSearchRequest(BaseModel):
    """
    模板搜索请求架构

    Schema for template search request
    """

    query: Optional[str] = Field("", description="搜索关键词 / Search query")
    resource_type: Optional[ResourceType] = Field(
        None, description="资源类型过滤 / Resource type filter"
    )
    tags: Optional[List[str]] = Field([], description="标签过滤 / Tag filter")
    page: int = Field(1, ge=1, description="页码 / Page number")
    page_size: int = Field(50, ge=1, le=100, description="每页大小 / Page size")
