"""File handling utilities"""

import mimetypes
import uuid
from pathlib import Path
from typing import Op<PERSON>, <PERSON><PERSON>

import aiofiles
from fastapi import UploadFile, HTTPException

from app.core.config import settings
from app.utils.zip_handler import ZipHandler


class FileHandler:
    """Handles file upload, validation, and storage"""

    @staticmethod
    async def save_upload_file(upload_file: UploadFile, subdir: str = "") -> Path:
        """
        Save uploaded file to storage directory

        Args:
            upload_file: FastAPI UploadFile object
            subdir: Subdirectory within upload directory

        Returns:
            Path to saved file
        """
        # Validate file size
        if upload_file.size and upload_file.size > settings.max_file_size:
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size: {settings.max_file_size} bytes",
            )

        # Generate unique filename
        file_extension = (
            Path(upload_file.filename).suffix if upload_file.filename else ""
        )
        unique_filename = f"{uuid.uuid4()}{file_extension}"

        # Create target directory
        target_dir = settings.upload_dir / subdir
        target_dir.mkdir(parents=True, exist_ok=True)

        # Save file
        file_path = target_dir / unique_filename

        async with aiofiles.open(file_path, "wb") as f:
            content = await upload_file.read()
            await f.write(content)

        return file_path

    @staticmethod
    async def save_template_file(
        upload_file: UploadFile, name: str, resource_type: str
    ) -> Path:
        """
        Save template file with specific naming

        Args:
            upload_file: FastAPI UploadFile object
            name: Template name (will be used as filename)
            resource_type: Type of resource (docx_template, csl_style, etc.)

        Returns:
            Path to saved file
        """
        # Validate file size
        if upload_file.size and upload_file.size > settings.max_file_size:
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size: {settings.max_file_size} bytes",
            )

        # Determine file extension
        original_extension = (
            Path(upload_file.filename).suffix if upload_file.filename else ""
        )

        # Create filename
        filename = f"{name}{original_extension}"

        # Determine subdirectory based on resource type
        subdir_map = {
            "docx_template": "docx",
            "csl_style": "csl",
            "bib_file": "bib",
            "lua_filter": "filters",
        }
        subdir = subdir_map.get(resource_type, "other")

        # Create target directory
        target_dir = settings.template_dir / subdir
        target_dir.mkdir(parents=True, exist_ok=True)

        # Save file
        file_path = target_dir / filename

        async with aiofiles.open(file_path, "wb") as f:
            content = await upload_file.read()
            await f.write(content)

        return file_path

    @staticmethod
    def get_mime_type(file_path: Path) -> Optional[str]:
        """Get MIME type of file"""
        mime_type, _ = mimetypes.guess_type(str(file_path))
        return mime_type

    @staticmethod
    def get_file_size(file_path: Path) -> int:
        """Get file size in bytes"""
        return file_path.stat().st_size if file_path.exists() else 0

    @staticmethod
    def delete_file(file_path: Path) -> bool:
        """Delete file safely"""
        try:
            if file_path.exists():
                file_path.unlink()
                return True
            return False
        except Exception:
            return False

    @staticmethod
    def validate_file_extension(filename: str, allowed_extensions: list) -> bool:
        """Validate file extension"""
        if not filename:
            return False

        extension = Path(filename).suffix.lower()
        return extension in [ext.lower() for ext in allowed_extensions]

    @staticmethod
    async def save_and_process_upload_file(upload_file: UploadFile, subdir: str = "") -> Tuple[Path, Optional[Path]]:
        """
        Save uploaded file and process if it's a ZIP file

        Args:
            upload_file: FastAPI UploadFile object
            subdir: Subdirectory within upload directory

        Returns:
            Tuple of (main_file_path, extraction_directory_path)
            - If regular file: (file_path, None)
            - If ZIP file: (main_document_path, extraction_directory_path)

        Raises:
            HTTPException: If file is too large or ZIP processing fails
        """
        # First save the uploaded file
        saved_file_path = await FileHandler.save_upload_file(upload_file, subdir)

        # Check if it's a ZIP file
        if ZipHandler.is_zip_file(saved_file_path):
            try:
                # Extract ZIP and find main document
                extract_dir, main_file = ZipHandler.extract_zip(saved_file_path)

                # Clean up the original ZIP file
                FileHandler.delete_file(saved_file_path)

                return main_file, extract_dir

            except ValueError as e:
                # Clean up on failure
                FileHandler.delete_file(saved_file_path)
                raise HTTPException(
                    status_code=422,
                    detail=f"ZIP file processing failed: {str(e)}"
                )
            except Exception as e:
                # Clean up on failure
                FileHandler.delete_file(saved_file_path)
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to process ZIP file: {str(e)}"
                )
        else:
            # Regular file
            return saved_file_path, None

    @staticmethod
    def cleanup_extraction(extract_dir: Optional[Path]) -> bool:
        """
        Clean up extraction directory if it exists

        Args:
            extract_dir: Directory to clean up (can be None)

        Returns:
            True if cleanup was successful or not needed
        """
        if extract_dir is None:
            return True
        return ZipHandler.cleanup_extraction(extract_dir)

    @staticmethod
    def is_zip_file(filename: str) -> bool:
        """
        Check if filename indicates a ZIP file

        Args:
            filename: Name of the file

        Returns:
            True if filename has ZIP extension
        """
        if not filename:
            return False
        return Path(filename).suffix.lower() == '.zip'
