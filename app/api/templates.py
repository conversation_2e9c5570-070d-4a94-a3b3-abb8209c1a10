"""Template management API endpoints"""

import json
from typing import Optional

from fastapi import (
    APIRouter,
    Depends,
    File,
    Form,
    HTTPException,
    Path,
    Query,
    UploadFile,
)
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.template_manager import TemplateManager
from app.models.template import TemplateResource, ResourceType
from app.schemas.template import (
    TemplateResourceResponse,
    TemplateResourceUpdate,
    TemplateResourceList,
    TemplateReloadResponse,
    TemplateSearchRequest,
)
from app.utils.file_handler import FileHandler

router = APIRouter(prefix="/templates", tags=["模板管理"])


@router.post(
    "/",
    response_model=TemplateResourceResponse,
    summary="上传模板文件",
    description="上传新的模板或资源文件到系统中",
)
async def upload_template(
    file: UploadFile = File(..., description="模板文件 / Template file"),
    name: str = Form(..., description="唯一的模板名称 / Unique template name"),
    display_name: str = Form(..., description="显示名称 / Display name"),
    resource_type: ResourceType = Form(..., description="资源类型 / Resource type"),
    description: Optional[str] = Form(
        None, description="模板描述 / Template description"
    ),
    metadata: Optional[str] = Form(
        None, description="模板元数据 (JSON格式) / Template metadata (JSON format)"
    ),
    db: Session = Depends(get_db),
):
    """
    上传新的模板或资源文件

    Upload a new template or resource file

    支持的文件类型 / Supported file types:
    - DOCX模板 / DOCX templates (.docx)
    - CSL样式文件 / CSL style files (.csl)
    - Lua过滤器 / Lua filters (.lua)
    - 其他资源文件 / Other resource files
    """

    # Parse metadata if provided
    metadata_dict = {}
    if metadata:
        try:
            metadata_dict = json.loads(metadata)
        except json.JSONDecodeError:
            raise HTTPException(
                status_code=400, detail="Invalid JSON in metadata field"
            )

    # Check if name already exists
    existing = db.query(TemplateResource).filter(TemplateResource.name == name).first()
    if existing:
        raise HTTPException(status_code=400, detail="Template name already exists")

    # Validate file extension based on resource type
    allowed_extensions = {
        ResourceType.DOCX_TEMPLATE: [".docx"],
        ResourceType.CSL_STYLE: [".csl"],
        ResourceType.LUA_FILTER: [".lua"],
        ResourceType.OTHER: [],  # Allow any extension
    }

    if resource_type in allowed_extensions and allowed_extensions[resource_type]:
        if not FileHandler.validate_file_extension(
            file.filename, allowed_extensions[resource_type]
        ):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid file extension for {resource_type}. Allowed: {allowed_extensions[resource_type]}",
            )

    try:
        # Save file
        file_path = await FileHandler.save_template_file(
            file, name, resource_type.value
        )

        # Create database record
        template = TemplateResource(
            name=name,
            display_name=display_name,
            description=description,
            resource_type=resource_type.value,
            file_path=str(file_path),
            file_size=FileHandler.get_file_size(file_path),
            mime_type=FileHandler.get_mime_type(file_path),
            is_builtin=False,
            metadata=metadata_dict,
        )

        db.add(template)
        db.commit()
        db.refresh(template)

        return template

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to upload template: {str(e)}"
        )


@router.get(
    "/",
    response_model=TemplateResourceList,
    summary="获取模板列表",
    description="获取所有模板的分页列表，支持按类型过滤",
)
async def list_templates(
    resource_type: Optional[ResourceType] = Query(
        None, description="按资源类型过滤 / Filter by resource type"
    ),
    active_only: bool = Query(
        True, description="仅显示活跃的模板 / Show only active templates"
    ),
    page: int = Query(1, ge=1, description="页码 / Page number"),
    page_size: int = Query(50, ge=1, le=100, description="每页大小 / Page size"),
    db: Session = Depends(get_db),
):
    """
    获取模板列表

    List all templates with optional filtering

    支持的过滤选项 / Supported filtering options:
    - 按资源类型过滤 / Filter by resource type
    - 仅显示活跃模板 / Show only active templates
    - 分页支持 / Pagination support
    """

    query = db.query(TemplateResource)

    # Apply filters
    if resource_type:
        query = query.filter(TemplateResource.resource_type == resource_type.value)

    if active_only:
        query = query.filter(TemplateResource.is_active.is_(True))

    # Get total count
    total = query.count()

    # Apply pagination
    offset = (page - 1) * page_size
    resources = query.offset(offset).limit(page_size).all()

    return TemplateResourceList(
        resources=resources, total=total, page=page, page_size=page_size
    )


@router.get(
    "/{template_id}",
    response_model=TemplateResourceResponse,
    summary="获取模板详情",
    description="根据ID获取特定模板的详细信息",
)
async def get_template(
    template_id: int = Path(..., description="模板ID / Template ID"),
    db: Session = Depends(get_db),
):
    """
    获取模板详情

    Get template by ID

    返回指定ID的模板详细信息 / Returns detailed information for the specified template ID
    """

    template = (
        db.query(TemplateResource).filter(TemplateResource.id == template_id).first()
    )
    if not template:
        raise HTTPException(status_code=404, detail="模板未找到 / Template not found")

    return template


@router.put(
    "/{template_id}",
    response_model=TemplateResourceResponse,
    summary="更新模板",
    description="更新模板的元数据信息",
)
async def update_template(
    update_data: TemplateResourceUpdate,
    template_id: int = Path(..., description="模板ID / Template ID"),
    db: Session = Depends(get_db),
):
    """
    更新模板元数据

    Update template metadata

    可更新的字段 / Updatable fields:
    - 显示名称 / Display name
    - 描述 / Description
    - 活跃状态 / Active status
    - 元数据 / Metadata
    """

    template = (
        db.query(TemplateResource).filter(TemplateResource.id == template_id).first()
    )
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")

    # Update fields
    update_dict = update_data.model_dump(exclude_unset=True)
    for field, value in update_dict.items():
        setattr(template, field, value)

    try:
        db.commit()
        db.refresh(template)
        return template
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to update template: {str(e)}"
        )


@router.delete(
    "/{template_id}", summary="删除模板", description="从系统中删除指定的模板文件"
)
async def delete_template(
    template_id: int = Path(..., description="模板ID / Template ID"),
    db: Session = Depends(get_db),
):
    """
    删除模板

    Delete template

    注意：此操作将同时删除文件系统中的文件和数据库记录，且不可恢复
    Warning: This operation will delete both the file from filesystem and database record, and cannot be undone
    """

    template = (
        db.query(TemplateResource).filter(TemplateResource.id == template_id).first()
    )
    if not template:
        raise HTTPException(status_code=404, detail="模板未找到 / Template not found")

    try:
        # Delete file from disk
        FileHandler.delete_file(template.get_absolute_path())

        # Delete from database
        db.delete(template)
        db.commit()

        return {"message": "模板删除成功 / Template deleted successfully"}

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"删除模板失败 / Failed to delete template: {str(e)}",
        )


@router.get(
    "/types/", summary="获取资源类型", description="获取系统支持的所有资源类型列表"
)
async def get_resource_types():
    """
    获取可用的资源类型

    Get available resource types

    返回系统支持的所有模板和资源文件类型
    Returns all template and resource file types supported by the system
    """

    return {
        "resource_types": [
            {
                "value": rt.value,
                "name": rt.value.replace("_", " ").title(),
                "description": {
                    ResourceType.DOCX_TEMPLATE: "Microsoft Word 文档模板 / Microsoft Word document templates",
                    ResourceType.CSL_STYLE: "引用样式语言文件 / Citation Style Language files",
                    ResourceType.LUA_FILTER: "Pandoc Lua 过滤器 / Pandoc Lua filters",
                    ResourceType.OTHER: "其他资源文件 / Other resource files",
                }[rt],
            }
            for rt in ResourceType
        ]
    }


@router.post(
    "/reload",
    response_model=TemplateReloadResponse,
    summary="重新加载模板",
    description="从文件系统重新加载所有模板",
)
async def reload_templates(db: Session = Depends(get_db)):
    """
    从文件系统重新加载模板

    Reload templates from filesystem

    此操作将扫描模板目录并更新数据库中的模板信息
    This operation will scan template directories and update template information in the database
    """

    try:
        template_manager = TemplateManager(db)
        results = await template_manager.reload_templates()

        return TemplateReloadResponse(
            success=True,
            message="模板重新加载成功 / Templates reloaded successfully",
            builtin_loaded=results["builtin_loaded"],
            user_loaded=results["user_loaded"],
            additional_loaded=results["additional_loaded"],
            errors=results["errors"],
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"重新加载模板失败 / Failed to reload templates: {str(e)}",
        )


@router.post(
    "/search",
    response_model=TemplateResourceList,
    summary="搜索模板",
    description="使用高级过滤条件搜索模板",
)
async def search_templates(
    search_request: TemplateSearchRequest, db: Session = Depends(get_db)
):
    """
    使用高级过滤搜索模板

    Search templates with advanced filtering

    支持的搜索条件 / Supported search criteria:
    - 关键词搜索 / Keyword search
    - 资源类型过滤 / Resource type filtering
    - 标签过滤 / Tag filtering
    - 分页支持 / Pagination support
    """

    try:
        template_manager = TemplateManager(db)
        templates = template_manager.search_templates(
            query=search_request.query,
            resource_type=search_request.resource_type,
            tags=search_request.tags,
        )

        # Apply pagination
        total = len(templates)
        offset = (search_request.page - 1) * search_request.page_size
        paginated_templates = templates[offset : offset + search_request.page_size]

        return TemplateResourceList(
            resources=paginated_templates,
            total=total,
            page=search_request.page,
            page_size=search_request.page_size,
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Template search failed: {str(e)}")


@router.get("/builtin/", summary="获取内置模板", description="获取系统内置的模板列表")
async def list_builtin_templates(
    resource_type: Optional[ResourceType] = Query(
        None, description="按资源类型过滤 / Filter by resource type"
    ),
    db: Session = Depends(get_db),
):
    """
    获取内置模板列表

    List built-in templates

    返回系统预装的内置模板，这些模板不能被删除
    Returns system pre-installed built-in templates that cannot be deleted
    """

    query = db.query(TemplateResource).filter(
        TemplateResource.is_builtin.is_(True), TemplateResource.is_active.is_(True)
    )

    if resource_type:
        query = query.filter(TemplateResource.resource_type == resource_type.value)

    templates = query.all()

    return {"templates": templates, "total": len(templates)}
