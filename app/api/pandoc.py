"""Pandoc conversion API endpoints"""

import json
import time
from pathlib import Path
from typing import Optional

from fastapi import APIRouter, Depends, File, Form, HTTPException, Path, UploadFile
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.pandoc_runner import PandocR<PERSON><PERSON>
from app.schemas.pandoc import PandocConversionResponse, PandocError
from app.utils.file_handler import FileHandler
from app.utils.zip_handler import <PERSON>ipHandler
from app.core.config import settings
from app.core.logging_config import get_logger, log_success, log_error, log_warning
from app.core.exception_handlers import (
    PandocConversionError,
    TemplateNotFoundError,
    FileProcessingError,
    ValidationError,
)

router = APIRouter(prefix="/convert", tags=["文档转换"])
logger = get_logger(__name__)


@router.post(
    "/",
    response_model=PandocConversionResponse,
    summary="文档转换",
    description="使用Pandoc将文档从一种格式转换为另一种格式",
)
async def convert_document(
    file: UploadFile = File(..., description="输入文档文件或ZIP压缩包 / Input document file or ZIP archive"),
    from_format: Optional[str] = Form(
        None,
        description="输入格式 (未指定时自动检测) / Input format (auto-detect if not specified)",
    ),
    to_format: str = Form(..., description="输出格式 / Output format"),
    docx_template: Optional[str] = Form(
        None, description="DOCX模板名称 / DOCX template name"
    ),
    csl_style: Optional[str] = Form(None, description="CSL样式名称 / CSL style name"),
    options: Optional[str] = Form(
        None,
        description="额外的Pandoc选项 (JSON格式) / Additional Pandoc options (JSON format)",
    ),
    metadata: Optional[str] = Form(
        None, description="文档元数据 (JSON格式) / Document metadata (JSON format)"
    ),
    output_filename: Optional[str] = Form(
        None, description="自定义输出文件名 / Custom output filename"
    ),
    db: Session = Depends(get_db),
):
    """
    使用Pandoc转换文档

    Convert document using Pandoc

    支持多种输入和输出格式，包括：
    Supports various input and output formats, including:
    - Markdown, HTML, DOCX, PDF, LaTeX 等
    - Markdown, HTML, DOCX, PDF, LaTeX, etc.

    输入文件支持 / Input file support:
    - 单个文档文件 / Single document files
    - ZIP压缩包（自动识别主文档，支持图片等资源文件）/ ZIP archives (auto-detect main document, supports images and resources)

    可选功能 / Optional features:
    - 使用自定义DOCX模板 / Use custom DOCX templates
    - 应用CSL引用样式 / Apply CSL citation styles
    - 添加自定义元数据 / Add custom metadata
    - 指定额外的Pandoc选项 / Specify additional Pandoc options
    """

    try:
        logger.info(
            f"[bold blue]Starting conversion[/bold blue]: {file.filename} -> {to_format}",
            extra={"markup": True}
        )

        # Parse JSON fields
        options_dict = {}
        metadata_dict = {}

        if options:
            try:
                options_dict = json.loads(options)
                logger.debug(f"Parsed options: {options_dict}")
            except json.JSONDecodeError as e:
                raise ValidationError(
                    message="Invalid JSON in options field",
                    field="options",
                    details={"json_error": str(e)}
                )

        if metadata:
            try:
                metadata_dict = json.loads(metadata)
                logger.debug(f"Parsed metadata: {metadata_dict}")
            except json.JSONDecodeError as e:
                raise ValidationError(
                    message="Invalid JSON in metadata field",
                    field="metadata",
                    details={"json_error": str(e)}
                )

        # Save and process uploaded file (handles both regular files and ZIP archives)
        extract_dir = None
        try:
            input_file, extract_dir = await FileHandler.save_and_process_upload_file(file, "input")
            if extract_dir:
                log_success(logger, f"ZIP archive extracted and main file identified: {input_file.name}")
            else:
                log_success(logger, f"Uploaded file saved: {input_file.name}")
        except Exception as e:
            raise FileProcessingError(
                message="Failed to save or process uploaded file",
                filename=file.filename,
                details={"error": str(e)}
            )

        # Initialize Pandoc runner
        runner = PandocRunner(db)

        # Convert document
        success, output_files, message, conversion_time = await runner.convert(
            input_file=input_file,
            from_format=from_format,
            to_format=to_format,
            docx_template=docx_template,
            csl_style=csl_style,
            options=options_dict,
            metadata=metadata_dict,
            output_filename=output_filename,
        )

        # Cleanup input file and extraction directory after conversion attempt
        try:
            FileHandler.delete_file(input_file)
            logger.debug(f"Cleaned up input file: {input_file.name}")
        except Exception as e:
            log_warning(logger, f"Failed to cleanup input file {input_file.name}: {e}")

        # Cleanup extraction directory if it exists
        if extract_dir:
            try:
                FileHandler.cleanup_extraction(extract_dir)
                logger.debug(f"Cleaned up extraction directory: {extract_dir}")
            except Exception as e:
                log_warning(logger, f"Failed to cleanup extraction directory {extract_dir}: {e}")

        if not success:
            log_error(logger, f"Conversion failed: {message}")
            raise PandocConversionError(
                message=message,
                details={
                    "conversion_time": conversion_time,
                    "from_format": from_format,
                    "to_format": to_format,
                }
            )

        # Handle multiple files
        download_url = None
        output_file_names = [f.name for f in output_files]

        if len(output_files) == 1:
            # Single file - direct download
            download_url = f"/download/{output_files[0].name}"
            log_success(logger, f"Single file conversion completed: {output_files[0].name}")
        elif len(output_files) > 1:
            # Multiple files - create ZIP
            zip_name = f"output_{int(time.time())}.zip"
            zip_path = settings.output_dir / zip_name
            try:
                ZipHandler.create_zip(output_files, zip_path, "output")
                download_url = f"/download/{zip_name}"
                output_file_names.append(zip_name)
                log_success(logger, f"Multiple files zipped: {zip_name}")
            except Exception as e:
                raise FileProcessingError(
                    message="Failed to create ZIP archive",
                    details={"files": output_file_names, "error": str(e)}
                )

        log_success(
            logger,
            f"Conversion completed in {conversion_time:.2f}s: "
            f"{file.filename} -> {', '.join(output_file_names)}"
        )

        return PandocConversionResponse(
            success=True,
            message=message,
            output_files=output_file_names,
            download_url=download_url,
            conversion_time=conversion_time,
        )

    except (PandocConversionError, TemplateNotFoundError, FileProcessingError, ValidationError):
        # Re-raise custom exceptions (they will be handled by exception handlers)
        raise
    except Exception as e:
        log_error(logger, f"Unexpected error during conversion: {e}")
        raise PandocConversionError(
            message="An unexpected error occurred during conversion",
            details={"error": str(e), "filename": file.filename}
        )


@router.get("/download/{filename}", summary="下载文件", description="下载转换后的文件")
async def download_file(filename: str = Path(..., description="文件名 / Filename")):
    """
    下载转换后的文件

    Download converted file

    支持下载单个文件或包含多个文件的ZIP压缩包
    Supports downloading single files or ZIP archives containing multiple files
    """

    file_path = settings.output_dir / filename

    logger.info(f"Download requested: {filename}")

    if not file_path.exists():
        log_warning(logger, f"File not found for download: {filename}")
        raise HTTPException(status_code=404, detail="文件未找到 / File not found")

    try:
        # Determine media type
        media_type = FileHandler.get_mime_type(file_path) or "application/octet-stream"

        log_success(logger, f"File download started: {filename} ({media_type})")

        return FileResponse(path=str(file_path), filename=filename, media_type=media_type)
    except Exception as e:
        log_error(logger, f"Failed to serve file {filename}: {e}")
        raise FileProcessingError(
            message="Failed to serve file for download",
            filename=filename,
            details={"error": str(e)}
        )


@router.get(
    "/formats",
    summary="获取支持的格式",
    description="获取Pandoc支持的输入和输出格式列表",
)
async def get_supported_formats():
    """
    获取支持的输入和输出格式列表

    Get list of supported input and output formats

    返回Pandoc支持的所有文档格式
    Returns all document formats supported by Pandoc
    """

    # Common Pandoc formats
    input_formats = [
        "markdown",
        "markdown_strict",
        "markdown_phpextra",
        "markdown_github",
        "commonmark",
        "textile",
        "rst",
        "html",
        "docbook",
        "t2t",
        "docx",
        "odt",
        "epub",
        "opml",
        "org",
        "mediawiki",
        "twiki",
        "haddock",
        "latex",
        "json",
        "native",
    ]

    output_formats = [
        "html",
        "html4",
        "html5",
        "s5",
        "slidy",
        "slideous",
        "dzslides",
        "revealjs",
        "docx",
        "odt",
        "epub",
        "epub3",
        "fb2",
        "asciidoc",
        "markdown",
        "markdown_strict",
        "markdown_phpextra",
        "markdown_github",
        "commonmark",
        "rst",
        "textile",
        "latex",
        "beamer",
        "context",
        "man",
        "mediawiki",
        "dokuwiki",
        "zimwiki",
        "haddock",
        "rtf",
        "opml",
        "docbook",
        "docbook4",
        "docbook5",
        "jats",
        "opendocument",
        "json",
        "native",
        "pdf",
    ]

    return {
        "input_formats": sorted(input_formats),
        "output_formats": sorted(output_formats),
    }
