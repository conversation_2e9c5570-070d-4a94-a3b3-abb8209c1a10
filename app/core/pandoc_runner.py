"""Pandoc command runner and file processor"""

import asyncio
import shutil
import tempfile
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

from sqlalchemy.orm import Session

from app.core.config import settings
from app.models.template import TemplateResource, ResourceType


class PandocRunner:
    """Handles Pandoc command execution and file processing"""

    def __init__(self, db: Session):
        self.db = db
        self.temp_dir = None

    async def convert(
        self,
        input_file: Path,
        from_format: Optional[str],
        to_format: str,
        docx_template: Optional[str] = None,
        csl_style: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        output_filename: Optional[str] = None,
    ) -> Tuple[bool, List[Path], str, float]:
        """
        Convert file using Pandoc

        Returns:
            (success, output_files, message/error, conversion_time)
        """
        start_time = time.time()

        # Create temporary working directory
        self.temp_dir = Path(tempfile.mkdtemp())

        try:
            # Check if input file exists before processing
            if not input_file.exists():
                return False, [], f"Input file does not exist: {input_file}", time.time() - start_time

            # Build Pandoc command
            cmd = await self._build_command(
                input_file,
                from_format,
                to_format,
                docx_template,
                csl_style,
                options,
                metadata,
                output_filename,
            )

            # Double-check file exists right before execution
            if not input_file.exists():
                return False, [], f"Input file was deleted before Pandoc execution: {input_file}", time.time() - start_time

            # Execute Pandoc
            success, output, error = await self._execute_pandoc(cmd)

            if not success:
                return False, [], error, time.time() - start_time

            # Find output files
            output_files = self._find_output_files()

            # Move output files to output directory
            final_files = await self._move_output_files(output_files)

            return (
                True,
                final_files,
                "Conversion completed successfully",
                time.time() - start_time,
            )

        except Exception as e:
            return False, [], f"Conversion failed: {str(e)}", time.time() - start_time

        finally:
            # Cleanup temporary directory
            if self.temp_dir and self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)

    async def _build_command(
        self,
        input_file: Path,
        from_format: Optional[str],
        to_format: str,
        docx_template: Optional[str],
        csl_style: Optional[str],
        options: Optional[Dict[str, Any]],
        metadata: Optional[Dict[str, Any]],
        output_filename: Optional[str],
    ) -> List[str]:
        """Build Pandoc command"""

        cmd = ["pandoc"]

        # Input file (use absolute path to avoid issues with cwd)
        cmd.extend([str(input_file.resolve())])

        # Input format
        if from_format:
            cmd.extend(["-f", from_format])

        # Output format
        cmd.extend(["-t", to_format])

        # Output file
        if not output_filename:
            output_filename = f"output.{self._get_extension(to_format)}"

        output_path = self.temp_dir / output_filename
        cmd.extend(["-o", str(output_path)])

        # Template files from database
        if docx_template:
            template_path = await self._get_resource_path(
                docx_template, ResourceType.DOCX_TEMPLATE
            )
            if template_path:
                cmd.extend(["--reference-doc", str(template_path)])

        if csl_style:
            csl_path = await self._get_resource_path(csl_style, ResourceType.CSL_STYLE)
            if csl_path:
                cmd.extend(["--csl", str(csl_path)])

        # Additional options
        if options:
            for key, value in options.items():
                if value is True:
                    cmd.append(f"--{key}")
                elif value is not False and value is not None:
                    cmd.extend([f"--{key}", str(value)])

        # Metadata
        if metadata:
            for key, value in metadata.items():
                cmd.extend(["-M", f"{key}={value}"])

        return cmd

    async def _get_resource_path(
        self, name: str, resource_type: ResourceType
    ) -> Optional[Path]:
        """Get path to resource file from database"""
        resource = (
            self.db.query(TemplateResource)
            .filter(
                TemplateResource.name == name,
                TemplateResource.resource_type == resource_type,
                TemplateResource.is_active == True,
            )
            .first()
        )

        if resource and resource.file_exists:
            return resource.get_absolute_path()

        return None

    async def _execute_pandoc(self, cmd: List[str]) -> Tuple[bool, str, str]:
        """Execute Pandoc command"""
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=self.temp_dir,
            )

            stdout, stderr = await asyncio.wait_for(
                process.communicate(), timeout=settings.pandoc_timeout
            )

            if process.returncode == 0:
                return True, stdout.decode(), stderr.decode()
            else:
                return False, stdout.decode(), stderr.decode()

        except asyncio.TimeoutError:
            return False, "", "Pandoc execution timed out"
        except Exception as e:
            return False, "", f"Failed to execute Pandoc: {str(e)}"

    def _find_output_files(self) -> List[Path]:
        """Find all output files in temp directory"""
        if not self.temp_dir:
            return []

        output_files = []
        for file_path in self.temp_dir.iterdir():
            if file_path.is_file() and not file_path.name.startswith("."):
                output_files.append(file_path)

        return output_files

    async def _move_output_files(self, output_files: List[Path]) -> List[Path]:
        """Move output files to final output directory"""
        final_files = []

        for file_path in output_files:
            # Generate unique filename
            timestamp = int(time.time() * 1000)
            final_name = f"{timestamp}_{file_path.name}"
            final_path = settings.output_dir / final_name

            # Move file
            shutil.move(str(file_path), str(final_path))
            final_files.append(final_path)

        return final_files

    def _get_extension(self, format_name: str) -> str:
        """Get file extension for format"""
        extensions = {
            "html": "html",
            "pdf": "pdf",
            "docx": "docx",
            "odt": "odt",
            "epub": "epub",
            "latex": "tex",
            "markdown": "md",
            "rst": "rst",
            "txt": "txt",
        }
        return extensions.get(format_name, "out")
