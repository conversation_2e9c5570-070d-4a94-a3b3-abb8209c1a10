"""HTTP request/response logging middleware with Rich formatting"""

import time
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.logging_config import get_logger


class HTTPLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware to log HTTP requests and responses with Rich formatting"""
    
    def __init__(self, app, logger_name: str = "http"):
        super().__init__(app)
        self.logger = get_logger(logger_name)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Start timing
        start_time = time.time()
        
        # Get client info
        client_ip = self.get_client_ip(request)
        
        # Log request
        self.log_request(request, client_ip)
        
        # Process request
        try:
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log response
            self.log_response(request, response, client_ip, process_time)
            
            # Add processing time header
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # Calculate processing time for errors
            process_time = time.time() - start_time
            
            # Log error
            self.log_error(request, e, client_ip, process_time)
            
            # Re-raise the exception
            raise
    
    def get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request"""
        # Check for forwarded headers first (for reverse proxies)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        if request.client:
            return request.client.host
        
        return "unknown"
    
    def log_request(self, request: Request, client_ip: str):
        """Log incoming HTTP request"""
        method = request.method
        url = str(request.url)
        user_agent = request.headers.get("User-Agent", "unknown")
        
        # Color code by method
        method_colors = {
            "GET": "green",
            "POST": "blue", 
            "PUT": "yellow",
            "DELETE": "red",
            "PATCH": "magenta",
            "HEAD": "cyan",
            "OPTIONS": "white",
        }
        
        method_color = method_colors.get(method, "white")
        
        self.logger.info(
            f"[bold {method_color}]{method}[/bold {method_color}] "
            f"[cyan]{url}[/cyan] - "
            f"[dim]Client: {client_ip}[/dim]",
            extra={"markup": True}
        )
        
        # Log user agent in debug mode
        self.logger.debug(f"User-Agent: {user_agent}")
    
    def log_response(self, request: Request, response: Response, client_ip: str, process_time: float):
        """Log HTTP response"""
        method = request.method
        url = str(request.url)
        status_code = response.status_code
        
        # Color code by status
        if 200 <= status_code < 300:
            status_color = "green"
            status_icon = "✓"
        elif 300 <= status_code < 400:
            status_color = "yellow"
            status_icon = "↻"
        elif 400 <= status_code < 500:
            status_color = "orange3"
            status_icon = "⚠"
        else:
            status_color = "red"
            status_icon = "✗"
        
        # Color code by method
        method_colors = {
            "GET": "green",
            "POST": "blue",
            "PUT": "yellow", 
            "DELETE": "red",
            "PATCH": "magenta",
            "HEAD": "cyan",
            "OPTIONS": "white",
        }
        
        method_color = method_colors.get(method, "white")
        
        # Format processing time
        if process_time < 0.1:
            time_color = "green"
        elif process_time < 0.5:
            time_color = "yellow"
        else:
            time_color = "red"
        
        self.logger.info(
            f"[bold {method_color}]{method}[/bold {method_color}] "
            f"[cyan]{url}[/cyan] - "
            f"[bold {status_color}]{status_icon} {status_code}[/bold {status_color}] - "
            f"[{time_color}]{process_time:.3f}s[/{time_color}] - "
            f"[dim]{client_ip}[/dim]",
            extra={"markup": True}
        )
        
        # Log response size if available
        content_length = response.headers.get("content-length")
        if content_length:
            self.logger.debug(f"Response size: {content_length} bytes")
    
    def log_error(self, request: Request, error: Exception, client_ip: str, process_time: float):
        """Log HTTP request that resulted in an error"""
        method = request.method
        url = str(request.url)
        
        method_colors = {
            "GET": "green",
            "POST": "blue",
            "PUT": "yellow",
            "DELETE": "red", 
            "PATCH": "magenta",
            "HEAD": "cyan",
            "OPTIONS": "white",
        }
        
        method_color = method_colors.get(method, "white")
        
        self.logger.error(
            f"[bold {method_color}]{method}[/bold {method_color}] "
            f"[cyan]{url}[/cyan] - "
            f"[bold red]✗ ERROR[/bold red] - "
            f"[red]{process_time:.3f}s[/red] - "
            f"[dim]{client_ip}[/dim] - "
            f"[red]{error.__class__.__name__}: {str(error)}[/red]",
            extra={"markup": True}
        )


def setup_http_logging(app, logger_name: str = "http.access"):
    """Setup HTTP request/response logging middleware"""
    app.add_middleware(HTTPLoggingMiddleware, logger_name=logger_name)
    
    logger = get_logger(__name__)
    logger.info("HTTP request/response logging middleware configured")


def configure_uvicorn_loggers():
    """Configure uvicorn loggers to work with Rich"""
    import logging
    from app.core.config import settings
    
    # Configure uvicorn access logger
    access_logger = logging.getLogger("uvicorn.access")
    access_logger.handlers.clear()  # Remove default handlers
    access_logger.setLevel(logging.WARNING)  # Reduce noise, we handle access logs in middleware
    
    # Configure uvicorn error logger  
    error_logger = logging.getLogger("uvicorn.error")
    error_logger.setLevel(logging.INFO)
    
    # Configure uvicorn main logger
    uvicorn_logger = logging.getLogger("uvicorn")
    uvicorn_logger.setLevel(logging.INFO)
    
    # Set specific levels based on debug mode
    if settings.debug:
        access_logger.setLevel(logging.DEBUG)
        error_logger.setLevel(logging.DEBUG)
        uvicorn_logger.setLevel(logging.DEBUG)
