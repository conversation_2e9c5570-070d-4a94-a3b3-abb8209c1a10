"""Template management system for loading and managing templates"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any

from sqlalchemy.orm import Session

from app.core.config import settings
from app.models.template import TemplateResource, ResourceType
from app.utils.file_handler import FileHandler

logger = logging.getLogger(__name__)


class TemplateMetadata:
    """Template metadata structure"""

    def __init__(
        self,
        name: str,
        display_name: str,
        description: str = "",
        version: str = "1.0.0",
        author: str = "",
        tags: List[str] = None,
        supported_formats: List[str] = None,
        custom_fields: Dict[str, Any] = None,
    ):
        self.name = name
        self.display_name = display_name
        self.description = description
        self.version = version
        self.author = author
        self.tags = tags or []
        self.supported_formats = supported_formats or []
        self.custom_fields = custom_fields or {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "name": self.name,
            "display_name": self.display_name,
            "description": self.description,
            "version": self.version,
            "author": self.author,
            "tags": self.tags,
            "supported_formats": self.supported_formats,
            "custom_fields": self.custom_fields,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "TemplateMetadata":
        """Create from dictionary"""
        return cls(
            name=data.get("name", ""),
            display_name=data.get("display_name", ""),
            description=data.get("description", ""),
            version=data.get("version", "1.0.0"),
            author=data.get("author", ""),
            tags=data.get("tags", []),
            supported_formats=data.get("supported_formats", []),
            custom_fields=data.get("custom_fields", {}),
        )


class TemplateManager:
    """Manages template loading, scanning, and metadata"""

    def __init__(self, db: Session):
        self.db = db
        self.template_dir = settings.template_dir
        self.builtin_templates_dir = self.template_dir / "builtin"
        self.user_templates_dir = self.template_dir / "user"
        self.additional_template_dirs = settings.get_additional_template_dirs()

        # Ensure directories exist
        self.builtin_templates_dir.mkdir(parents=True, exist_ok=True)
        self.user_templates_dir.mkdir(parents=True, exist_ok=True)

    async def scan_and_load_templates(self) -> Dict[str, int]:
        """Scan for templates and load them into database"""
        results = {
            "builtin_loaded": 0,
            "user_loaded": 0,
            "additional_loaded": 0,
            "errors": 0,
        }

        # Load built-in templates
        builtin_count, builtin_errors = await self._scan_directory(
            self.builtin_templates_dir, is_builtin=True, source="builtin"
        )
        results["builtin_loaded"] = builtin_count
        results["errors"] += builtin_errors

        # Load user templates
        user_count, user_errors = await self._scan_directory(
            self.user_templates_dir, is_builtin=False, source="user"
        )
        results["user_loaded"] = user_count
        results["errors"] += user_errors

        # Load additional template directories
        additional_count = 0
        for additional_dir in self.additional_template_dirs:
            if additional_dir.exists():
                count, errors = await self._scan_directory(
                    additional_dir,
                    is_builtin=False,
                    source=f"additional:{additional_dir.name}",
                )
                additional_count += count
                results["errors"] += errors
            else:
                logger.warning(
                    f"Additional template directory does not exist: {additional_dir}"
                )

        results["additional_loaded"] = additional_count

        return results

    async def _scan_directory(
        self, directory: Path, is_builtin: bool, source: str
    ) -> tuple[int, int]:
        """Scan a directory for templates"""
        loaded_count = 0
        error_count = 0

        if not directory.exists():
            return loaded_count, error_count

        # Scan for different resource types
        type_mappings = {
            ResourceType.DOCX_TEMPLATE: ["*.docx"],
            ResourceType.CSL_STYLE: ["*.csl"],
            ResourceType.LUA_FILTER: ["*.lua"],
            ResourceType.OTHER: ["*"],
        }

        for resource_type, patterns in type_mappings.items():
            for pattern in patterns:
                for file_path in directory.glob(f"**/{pattern}"):
                    if file_path.is_file() and not file_path.name.endswith(".metadata"):
                        try:
                            await self._load_template_file(
                                file_path, resource_type, is_builtin, source
                            )
                            loaded_count += 1
                        except Exception as e:
                            logger.error(f"Failed to load template {file_path}: {e}")
                            error_count += 1

        return loaded_count, error_count

    async def _load_template_file(
        self,
        file_path: Path,
        resource_type: ResourceType,
        is_builtin: bool,
        source: str,
    ) -> Optional[TemplateResource]:
        """Load a single template file"""

        # Generate template name from file path and source
        if source.startswith("additional:"):
            # For additional directories, use source prefix + relative path
            source_name = source.split(":", 1)[1]
            template_name = f"{source_name}_{file_path.stem}"
        elif is_builtin:
            relative_path = file_path.relative_to(self.builtin_templates_dir)
            template_name = (
                str(relative_path.with_suffix("")).replace("/", "_").replace("\\", "_")
            )
        else:
            relative_path = file_path.relative_to(self.user_templates_dir)
            template_name = (
                str(relative_path.with_suffix("")).replace("/", "_").replace("\\", "_")
            )

        # Check if template already exists
        existing = (
            self.db.query(TemplateResource)
            .filter(TemplateResource.name == template_name)
            .first()
        )

        if existing:
            # Update existing template if file is newer
            if file_path.stat().st_mtime > existing.updated_at.timestamp():
                await self._update_existing_template(existing, file_path)
            return existing

        # Load metadata if available
        metadata = await self._load_template_metadata(file_path)

        # Create new template resource
        template = TemplateResource(
            name=template_name,
            display_name=metadata.display_name or file_path.stem,
            description=metadata.description,
            resource_type=resource_type.value,
            file_path=str(file_path.absolute()),
            file_size=file_path.stat().st_size,
            mime_type=FileHandler.get_mime_type(file_path),
            is_builtin=is_builtin,
            metadata=metadata.to_dict(),
        )

        self.db.add(template)
        self.db.commit()
        self.db.refresh(template)

        logger.info(f"Loaded template: {template_name} ({resource_type.value})")
        return template

    async def _load_template_metadata(self, file_path: Path) -> TemplateMetadata:
        """Load metadata for a template file"""

        # Look for metadata file (same name with .metadata extension)
        metadata_file = file_path.with_suffix(file_path.suffix + ".metadata")

        if metadata_file.exists():
            try:
                with open(metadata_file, "r", encoding="utf-8") as f:
                    metadata_dict = json.load(f)
                return TemplateMetadata.from_dict(metadata_dict)
            except Exception as e:
                logger.warning(f"Failed to load metadata for {file_path}: {e}")

        # Return default metadata
        return TemplateMetadata(
            name=file_path.stem,
            display_name=file_path.stem.replace("_", " ").title(),
            description=f"Template file: {file_path.name}",
        )

    async def _update_existing_template(
        self, template: TemplateResource, file_path: Path
    ) -> None:
        """Update an existing template with new file data"""

        template.file_size = file_path.stat().st_size
        template.mime_type = FileHandler.get_mime_type(file_path)

        # Update metadata if available
        metadata = await self._load_template_metadata(file_path)
        template.set_metadata(metadata.to_dict())

        self.db.commit()
        logger.info(f"Updated template: {template.name}")

    async def reload_templates(self) -> Dict[str, int]:
        """Reload all templates from filesystem"""
        logger.info("Reloading templates...")
        return await self.scan_and_load_templates()

    def get_template_by_name(self, name: str) -> Optional[TemplateResource]:
        """Get template by name"""
        return (
            self.db.query(TemplateResource)
            .filter(TemplateResource.name == name, TemplateResource.is_active == True)
            .first()
        )

    def get_templates_by_type(
        self, resource_type: ResourceType
    ) -> List[TemplateResource]:
        """Get all templates of a specific type"""
        return (
            self.db.query(TemplateResource)
            .filter(
                TemplateResource.resource_type == resource_type.value,
                TemplateResource.is_active == True,
            )
            .all()
        )

    def search_templates(
        self,
        query: str = "",
        resource_type: Optional[ResourceType] = None,
        tags: List[str] = None,
    ) -> List[TemplateResource]:
        """Search templates by query, type, and tags"""

        db_query = self.db.query(TemplateResource).filter(
            TemplateResource.is_active == True
        )

        if resource_type:
            db_query = db_query.filter(
                TemplateResource.resource_type == resource_type.value
            )

        if query:
            db_query = db_query.filter(
                TemplateResource.display_name.contains(query)
                | TemplateResource.description.contains(query)
            )

        templates = db_query.all()

        # Filter by tags if specified
        if tags:
            filtered_templates = []
            for template in templates:
                template_tags = template.get_metadata().get("tags", [])
                if any(tag in template_tags for tag in tags):
                    filtered_templates.append(template)
            return filtered_templates

        return templates
